﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="userAdd.aspx.cs" Inherits="Web1.userAdd" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>前台注册界面</title>
</head>
<body>
    <form id="form1" runat="server">
        <div>
            <h2>前台注册界面</h2>

            用户名:<asp:TextBox ID="txt_username" runat="server"></asp:TextBox>
            <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txt_username" ErrorMessage="*"></asp:RequiredFieldValidator>
            <br /><br />

            密码:<asp:TextBox ID="txt_password" runat="server" TextMode="Password"></asp:TextBox>
            <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="txt_password" ErrorMessage="*"></asp:RequiredFieldValidator>
            <br /><br />

            确认密码:<asp:TextBox ID="txt_confirm_password" runat="server" TextMode="Password"></asp:TextBox>
            <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ControlToValidate="txt_confirm_password" ErrorMessage="*"></asp:RequiredFieldValidator>
            <asp:CompareValidator ID="CompareValidator1" runat="server" ControlToValidate="txt_confirm_password" ControlToCompare="txt_password" ErrorMessage="密码不一致" ForeColor="Red"></asp:CompareValidator>
            <br /><br />

            邮箱:<asp:TextBox ID="txt_email" runat="server"></asp:TextBox>
            <asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" ControlToValidate="txt_email" ErrorMessage="*"></asp:RequiredFieldValidator>
            <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ControlToValidate="txt_email" ErrorMessage="邮箱格式不正确" ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" ForeColor="Red"></asp:RegularExpressionValidator>
            <br /><br />

            <asp:Button ID="btn_register" runat="server" OnClick="btn_register_Click" Text="注册" />
            <asp:Button ID="btn_back" runat="server" OnClick="btn_back_Click" Text="返回登录" CausesValidation="false" />
            <br /><br />

            <asp:Label ID="lbl_message" runat="server" Text="" ForeColor="Red"></asp:Label>

        </div>
    </form>
</body>
</html>
